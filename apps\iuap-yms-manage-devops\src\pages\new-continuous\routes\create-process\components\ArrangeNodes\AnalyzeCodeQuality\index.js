import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef, useCallback, useMemo } from 'react'
import { Checkbox, Col, Form, Row, Select } from 'iuap-gp-ymscloudui-fe/baseui';
import { inject, observer } from 'mobx-react';
import { getMainById } from 'newDevopsService/NewService';
import { CONF_APP_TYPE, comboLanguageTypeData } from '@/constants/newContinuous';

const { Option } = Select;
const { Item: FormItem } = Form;

const Sonar = forwardRef((props, ref) => {
  const { CreateProcessStore, taskinfo, appType, devopsType, id, isEdit } = props;

  // 初始化状态，移除未使用的字段
  const initialState = useMemo(() => ({
    canUseCheckmax: true,
    sonarFlag: false,
    langFlag: false,
    fortifyFlag: false,
    checkmaxFlag: false,
    allCheckType: false,
    dependencyCheck: false,
    sonarLanguageType: [CONF_APP_TYPE[appType]],
    checkmaxLanguageType: [CONF_APP_TYPE[appType]],
    langLanguageType: ["java"]
  }), [appType]);

  const [state, setState] = useState(initialState);
  const flagRef = useRef(true);

  // 获取流水线主信息
  const getMainByIdData = useCallback(async () => {
    const { baseData, updateMainData } = CreateProcessStore;
    if (!baseData.productId && id !== 'new') {
      try {
        const data = await getMainById({ devopsPipelineMainId: id });
        updateMainData(data);
      } catch (error) {
        console.error('获取流水线主信息失败:', error);
      }
    }
  }, [CreateProcessStore, id]);

  /**
   * 选择是否选中
   */
  const handleCheck = useCallback(field => value => {
    flagRef.current = true;
    setState(prevState => {
      const newState = { ...prevState, [field]: value };
      const { sonarLanguageType, checkmaxLanguageType, langLanguageType } = prevState;

      switch (field) {
        case "sonarFlag":
          newState.sonarLanguageType = value ? sonarLanguageType : ["java"];
          break;
        case "checkmaxFlag":
          newState.checkmaxLanguageType = value ? checkmaxLanguageType : ["java"];
          break;
        case "allCheckType":
          newState.checkmaxLanguageType = value ? checkmaxLanguageType : ["java"];
          if (!value) {
            newState.checkmaxFlag = false;
          }
          break;
        case "langFlag":
          newState.langLanguageType = value ? langLanguageType : ["java"];
          break;
        default:
          break;
      }

      return newState;
    });
  }, []);

  useEffect(() => {
    const type = [CONF_APP_TYPE[devopsType]];
    getMainByIdData();

    const params = taskinfo?.params || {};
    const {
      sonarLanguageType,
      checkmaxLanguageType,
      fortifyFlag,
      checkmaxFlag,
      allCheckType,
      langLanguageType
    } = params;

    setState(prevState => ({
      ...prevState,
      ...params,
      sonarLanguageType: sonarLanguageType ? sonarLanguageType.split(',') : type,
      checkmaxLanguageType: checkmaxLanguageType ? checkmaxLanguageType.split(',') : type,
      allCheckType: checkmaxFlag || fortifyFlag || allCheckType,
      langLanguageType: langLanguageType ? langLanguageType.split(',') : ["java"],
    }));
  }, [devopsType, taskinfo, getMainByIdData]);

  // 选择语言类型
  const handleChange = useCallback((field) => (value) => {
    flagRef.current = true;
    setState(prevState => ({
      ...prevState,
      [field]: value
    }));
  }, []);

  const saveData = useCallback(() => {
    const {
      sonarFlag,
      sonarLanguageType,
      checkmaxFlag,
      checkmaxLanguageType,
      allCheckType,
      langFlag,
      langLanguageType
    } = state;

    const data = {
      sonarFlag,
      sonarLanguageType: sonarLanguageType.join(','),
      checkmaxFlag,
      checkmaxLanguageType: checkmaxLanguageType.join(','),
      allCheckType,
      langFlag,
      langLanguageType: langLanguageType.join(',')
    };

    CreateProcessStore.updateNodeInfo(data);
  }, [state, CreateProcessStore]);

  const scrollToEnd = useCallback(() => {
    // 滚动到底部的逻辑
  }, []);

  // 使用 useImperativeHandle 暴露方法和属性
  useImperativeHandle(ref, () => ({
    saveData,
    flag: flagRef.current,
    state
  }), [saveData, state]);

  // 解构状态变量
  const { canUseCheckmax, sonarFlag, checkmaxFlag } = state;

  // 渲染语言类型选择器
  const renderLanguageSelect = useCallback((fieldId, value, onChange, disabled) => (
    <Select
      fieldid={fieldId}
      multiple
      value={value}
      style={{ width: "200px" }}
      searchPlaceholder={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFA", "语言类型") /* "语言类型" */}
      scrollToEnd={scrollToEnd}
      disabled={disabled}
      onChange={onChange}
    >
      {comboLanguageTypeData.map(item => (
        <Option key={item.value} value={item.value}>{item.name}</Option>
      ))}
    </Select>
  ), [scrollToEnd]);

  return (
    <div className="sonar">
      <div style={{ marginBottom: 30 }}>
        <div>
          <Row grid={12} style={{ padding: "20px 40px" }}>
            <Col lg={12} md={12} xs={12} sm={12} className="checkList">
              {canUseCheckmax && (
                <FormItem className="root-checkbox-sonar">
                  <Checkbox
                    checked={checkmaxFlag}
                    disabled={!isEdit}
                    style={{ marginRight: 10 }}
                    fieldid="canuse_checkbox"
                    onChange={handleCheck("checkmaxFlag")}
                  >
                    {lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D052C6804580034", "启用安全扫描") /* "启用安全扫描" */}
                  </Checkbox>
                </FormItem>
              )}
              {checkmaxFlag && (
                <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFB", "语言类型：") /* "语言类型：" */}>
                  {renderLanguageSelect(
                    "newFlow_BLM2nCZ7Om_select",
                    state.checkmaxLanguageType,
                    handleChange("checkmaxLanguageType"),
                    !isEdit
                  )}
                </FormItem>
              )}
            </Col>
            <Col lg={12} md={12} xs={12} sm={12} className="checkList">
              <FormItem className="root-checkbox-sonar">
                <Checkbox
                  checked={sonarFlag}
                  style={{ marginRight: 10 }}
                  disabled={!isEdit}
                  fieldid="sonar_checkbox"
                  onChange={handleCheck("sonarFlag")}
                >
                  {lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFF", "启用静态扫描") /* "启用静态扫描" */}
                </Checkbox>
              </FormItem>
              {sonarFlag && (
                <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFB", "语言类型：") /* "语言类型：" */}>
                  {renderLanguageSelect(
                    "newFlow_HXtV5iwEd4_select",
                    state.sonarLanguageType,
                    handleChange("sonarLanguageType"),
                    !isEdit
                  )}
                </FormItem>
              )}
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
});

export default inject('CreateProcessStore')(observer(Sonar));
